async function weeklyKrProgress({ dv }) {
  // 定义 getWeekNumber 函数
  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - ((d.getDay() + 6) % 7));
    const week1 = new Date(d.getFullYear(), 0, 4);
    return (
      1 +
      Math.round(((d - week1) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7)
    );
  }

  // 获取当前文件的路径信息，提取项目名称
  const projectName = dv.current().file.path.split("/")[1].trim();

  // 获取当前日期并计算年份和上周周数
  const now = new Date();
  let year = now.getFullYear();
  let weekNumber = getWeekNumber(now) - 1;

  // 初始化查找结果
  let krContent = null;
  let foundFilePath = null;

  // 尝试查找最近的周报（最多回溯4周）
  for (let i = 0; i < 4; i++) {
    // 处理跨年情况（如果当前是第一周，则上周是去年的最后一周）
    if (weekNumber < 1) {
      year--;
      weekNumber = 52; // ISO周数最大为52或53周
    }

    // 动态生成文件名
    const dynamicFilename = `Replay-${year}-WK${weekNumber
      .toString()
      .padStart(2, "0")}.md`;
    const path = `2-项目/${projectName}/3-每周评审/${dynamicFilename}`;
    const file = app.vault.getAbstractFileByPath(path);

    if (file) {
      // 读取并解析文件
      const content = await dv.io.load(path);
      const headingRegex =
        /(?:^|\n)#{1,6}\s*.*?KR进度.*?(?:\n|$)([\s\S]*?)(?=\n?#{1,6}\s|$)/i;
      const match = content.match(headingRegex);

      if (match) {
        krContent = match[1].trim();
        foundFilePath = path;
        break; // 找到有效内容，跳出循环
      }
    }

    // 准备回溯到更早的一周
    weekNumber--;
  }

  // 输出结果
  if (krContent) {
    // 解析表格内容并添加文件链接列
    const tableLines = krContent
      .split("\n")
      .filter((line) => line.trim().startsWith("|"));

    if (tableLines.length > 0) {
      // 提取文件名（不含路径和扩展名）
      const fileName = foundFilePath.split("/").pop().replace(".md", "");

      // 处理表头 - 在末尾添加文件链接列
      let headerLine = tableLines[0];
      if (!headerLine.includes("文件链接")) {
        // 移除末尾的管道符（如果有），然后添加新列
        headerLine = headerLine.replace(/\|\s*$/, "") + " | 文件链接 |";
      }

      // 处理分隔线 - 在末尾添加分隔符
      let separatorLine = tableLines[1];
      if (separatorLine && separatorLine.includes("---")) {
        // 移除末尾的管道符（如果有），然后添加分隔符
        separatorLine = separatorLine.replace(/\|\s*$/, "") + " | --- |";
      }

      // 处理数据行 - 在末尾添加文件链接
      const dataLines = tableLines.slice(2);
      const modifiedLines = dataLines.map((line) => {
        // 为每行数据在末尾添加文件链接 - 使用 HTML 格式确保链接完整
        const fileLink = `[${fileName}](${foundFilePath.replace(
          /\s/g,
          "%20"
        )})`;
        // 移除末尾的管道符（如果有），然后添加新列
        return line.replace(/\|\s*$/, "") + ` | ${fileLink} |`;
      });

      // 构建完整的表格
      const modifiedTable = [headerLine, separatorLine, ...modifiedLines].join(
        "\n"
      );
      dv.paragraph(modifiedTable);
    } else {
      // 如果不是表格格式，保持原有输出
      dv.paragraph(krContent);
    }
  } else {
    dv.el("p", "未找到近期项目周报");
  }
}

weeklyKrProgress(input);
