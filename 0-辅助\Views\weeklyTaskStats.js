function weeklyTaskStats({ dv }) {
  const fileName = dv.current().file.name;
  const weekMatch = fileName.match(/Review-(\d{4})-WK(\d+)/);
  if (!weekMatch) {
    dv.paragraph("**错误**：文件名格式不符合要求：Review-YYYY-WKWW");
    return;
  }
  
  const year = parseInt(weekMatch[1]);
  const currentWeek = parseInt(weekMatch[2]);
  const projectName = dv.current().file.path.split("/")[1];
  const planFileName = fileName.replace("Review", "Plan") + ".md";
  
  // ISO周数计算函数
  const getISOWeek = date => {
    const d = new Date(date);
    d.setHours(0,0,0,0);
    d.setDate(d.getDate() + 4 - (d.getDay() || 7));
    const yearStart = new Date(d.getFullYear(),0,1);
    return Math.ceil(((d - yearStart) / 86400000 + 1)/7);
  };
  
  // 收集任务数据
  let allTasks = [];
  
  // 1. 从规划文件获取任务
  const planPath = `2-项目/${projectName}/1-每周计划`;
  const planFile = dv.page(`${planPath}/${planFileName}`);
  if (planFile && planFile.file.tasks) {
    const planTasks = Array.from(planFile.file.tasks).filter(t => t.text && t.text.trim().length > 0);
    allTasks = allTasks.concat(planTasks);
  }
  
  // 2. 从技术债文件获取本周创建的任务
  const techDebtPath = `3-过程资产/${projectName}/技术债`;
  const techDebtPages = dv.pages(`"${techDebtPath}"`);
  const techDebtTasks = Array.from(techDebtPages.file.tasks).filter(t => t.text && t.text.trim().length > 0);
  
  const currentWeekTechDebtTasks = techDebtTasks.filter(t => {
    const createTimeMatch = t.text.match(/➕\s*(\d{4}-\d{2}-\d{2})/);
    if (createTimeMatch) {
      const createDate = new Date(createTimeMatch[1]);
      if (!isNaN(createDate.getTime())) {
        const taskWeek = getISOWeek(createDate);
        const taskYear = createDate.getFullYear();
        return taskYear === year && taskWeek === currentWeek;
      }
    }
    return false;
  });
  
  allTasks = allTasks.concat(currentWeekTechDebtTasks);
  
  // 输出统计结果
  if (allTasks.length > 0) {
    const totalTasks = allTasks.length;
    const completedTasks = allTasks.filter(t => t.checked);
    const pendingTasks = allTasks.filter(t => !t.checked);
    dv.el("p", `本周总计规划 ${totalTasks} 条任务，已完成 ${completedTasks.length} 条`);
    if (pendingTasks.length > 0) {
      dv.taskList(pendingTasks, false);
    }
  } else {
    dv.paragraph("⚠️ 未找到对应的规划文件或任务数据");
  }
}

weeklyTaskStats(input);