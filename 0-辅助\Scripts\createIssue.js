/**
 * 问题创建脚本（合并版）
 * 功能：
 * 1. 启动时弹出选择框让用户选择创建【1】新建阻碍 或 【2】新建技术债
 * 2. 根据选择调用对应的逻辑，零改动原有功能
 * 3. 保持所有原有参数、功能和输出不变
 */

const TEMPLATES = {
  BLOCKER: "0-辅助/Templater/Notes/TP-Project-Blocker.md",
  TECH_DEBT: "0-辅助/Templater/Notes/TP-Project-TechDebt.md",
};

const COMMENTS = {
  CANCELLED: "<!-- 操作已取消 -->",
  PROJECT_INFO_FAILED: "<!-- 获取项目信息失败 -->",
  TEMPLATE_NOT_FOUND: "<!-- 模板文件不存在 -->",
  ALIAS_CANCELLED: "<!-- 用户取消别名输入 -->",
  TYPE_SELECTION_CANCELLED: "<!-- 用户取消类型选择 -->",
};

async function getProjectInfo(tp) {
  const activeFile = tp.file.find_tfile(tp.file.title);
  if (!activeFile) {
    new Notice("未找到当前活动文件");
    return null;
  }
  const currentPath = activeFile.path;
  const projectName = currentPath.split("/")[1];
  const targetDir = `3-过程资产/${projectName}`;
  return { activeFile, currentPath, projectName, targetDir };
}

async function ensureDirectoryExists(dirPath) {
  const folder = app.vault.getAbstractFileByPath(dirPath);
  if (!folder) {
    await app.vault.createFolder(dirPath);
  }
}

async function readTemplate(templatePath) {
  const templateFile = app.vault.getAbstractFileByPath(templatePath);
  if (!templateFile) {
    new Notice(`模板文件不存在: ${templatePath}`);
    return null;
  }
  return await app.vault.read(templateFile);
}

function formatDateString(date) {
  return (
    date.getFullYear().toString() +
    (date.getMonth() + 1).toString().padStart(2, "0") +
    date.getDate().toString().padStart(2, "0")
  );
}

async function createBlocker(tp, projectInfo) {
  const { targetDir } = projectInfo;
  const blockerDir = `${targetDir}/阻碍`;
  await ensureDirectoryExists(blockerDir);
  
  const selectedText = tp.file.selection();
  const hasSelectedText = selectedText && selectedText.trim() !== "";
  
  const today = new Date();
  const dateStr = formatDateString(today);
  
  const existingFiles = app.vault
    .getFiles()
    .filter(file => 
      file.path.startsWith(blockerDir) &&
      file.name.startsWith(`blocker-${dateStr}-`) &&
      file.name.endsWith(".md")
    );
  
  const nextNumber = existingFiles.length + 1;
  const fileName = `blocker-${dateStr}-${nextNumber.toString().padStart(2, "0")}.md`;
  const filePath = `${blockerDir}/${fileName}`;
  
  // 先获取别名，再决定是否替换文本
  const defaultAlias = hasSelectedText ? selectedText : "";
  const aliasInput = await tp.system.prompt(
    "请输入障碍日志的别名",
    defaultAlias,
    false
  );
  
  if (aliasInput === null) {
    // 用户取消时，如果有选中文本，恢复原始内容
    if (hasSelectedText) {
      tp.file.cursor_append(selectedText);
    }
    return COMMENTS.ALIAS_CANCELLED;
  }
  
  // 只有在用户确认后才替换选中文本
  if (hasSelectedText) {
    const displayText = selectedText.trim();
    const linkText = `[[${fileName.replace('.md', '')}|${displayText}]]`;
    tp.file.cursor_append(linkText);
  }
  
  const templateContent = await readTemplate(TEMPLATES.BLOCKER);
  if (!templateContent) {
    return COMMENTS.TEMPLATE_NOT_FOUND;
  }
  
  let content = templateContent;
  const createdDate = today.toISOString().split("T")[0];
  content = content.replace('createdDate: ""', `createdDate: "${createdDate}"`);
  
  if (hasSelectedText) {
    content = content.replace('aliases:', `aliases: "${selectedText.trim()}"`);
  } else if (aliasInput && aliasInput.trim() !== "") {
    content = content.replace('aliases:', `aliases: "${aliasInput.trim()}"`);
  }
  
  content = content.replace("status: 进行中/待验证/已关闭", "status: 进行中");
  
  const newFile = await app.vault.create(filePath, content);
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(newFile);
  return newFile;
}

async function createTechDebt(tp, projectInfo) {
  const { targetDir } = projectInfo;
  const techDebtDir = `${targetDir}/技术债`;
  await ensureDirectoryExists(techDebtDir);
  
  const today = new Date();
  const dateStr = formatDateString(today);
  
  // 查找当天已存在的技术债文件，确定序号
  const existingFiles = app.vault
    .getFiles()
    .filter(
      (file) =>
        file.path.startsWith(techDebtDir) &&
        file.name.startsWith(`td-${dateStr}-`) &&
        file.name.endsWith(".md")
    );
  
  const nextNumber = existingFiles.length + 1;
  const fileName = `td-${dateStr}-${nextNumber
    .toString()
    .padStart(2, "0")}.md`;
  const filePath = `${techDebtDir}/${fileName}`;
  
  console.log("将创建文件:", filePath);
  
  // 弹出对话框让用户输入别名
  const aliasInput = await tp.system.prompt(
    "请输入技术债的别名：",
    "",
    false
  );
  
  if (aliasInput === null) {
    return COMMENTS.CANCELLED;
  }
  
  const alias = aliasInput.trim();
  console.log("用户输入的别名:", alias);
  
  const templateContent = await readTemplate(TEMPLATES.TECH_DEBT);
  if (!templateContent) {
    return COMMENTS.TEMPLATE_NOT_FOUND;
  }
  
  // 处理模板内容，替换变量
  let content = templateContent;
  
  // 替换创建日期
  const createdDate = today.toISOString().split("T")[0];
  content = content.replace("createdDate:", `createdDate: ${createdDate}`);
  
  // 替换别名 - 只有当别名不为空时才替换
  if (alias) {
    content = content.replace("aliases:", `aliases: ["${alias}"]`);
  }
  // 如果别名为空，保持 aliases: 不变
  
  // 设置状态为"待处理"
  content = content.replace(
    "status: 待处理/进行中/已关闭",
    "status: 待处理"
  );
  
  // 创建新文件
  const newFile = await app.vault.create(filePath, content);
  console.log("文件创建成功:", filePath);
  
  // 在新标签页中打开新创建的文件
  const leaf = app.workspace.getLeaf(true);
  await leaf.openFile(newFile);
  
  console.log("模板内容:", templateContent);
  console.log("替换后的内容:", content);
  console.log("替换后的别名:", alias);
  console.log("替换后的创建日期:", createdDate);
  console.log("替换后的状态:", "待处理");
  return newFile;
}

module.exports = async (tp) => {
  try {
    // 弹出选择框让用户选择创建类型
    const choice = await tp.system.suggester(
      ["【1】新建阻碍", "【2】新建技术债"],
      ["blocker", "techDebt"],
      false,
      "请选择要创建的类型"
    );
    
    if (!choice) {
      return COMMENTS.TYPE_SELECTION_CANCELLED;
    }
    
    // 获取项目信息
    const projectInfo = await getProjectInfo(tp);
    if (!projectInfo) {
      return COMMENTS.PROJECT_INFO_FAILED;
    }
    
    console.log("选择的项目名称:", projectInfo.projectName);
    console.log("选择的类型:", choice);
    
    // 根据选择调用对应的创建函数
    if (choice === "blocker") {
      return await createBlocker(tp, projectInfo);
    } else if (choice === "techDebt") {
      return await createTechDebt(tp, projectInfo);
    }
    
  } catch (error) {
    console.error("创建文件时出错:", error);
    new Notice(`创建文件时出错: ${error.message}`);
    return COMMENTS.PROJECT_INFO_FAILED;
  }
};