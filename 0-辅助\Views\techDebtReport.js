// 技术债报告视图组件
function techDebtReport({ dv }) {
    const projectName = dv.current().file.path.split("/")[1];
    const folder = `3-过程资产/${projectName}/技术债`;
    const pattern = /^td-\d{8}-\d{2}$/;
    const allFiles = dv.pages(`"${folder}"`);
    const combinedType = "阻塞型/成本型/战略型/无害型";
    
    const filteredFiles = allFiles.filter(p => {
        const name = p.file.name.replace(/\.md$/, ""); 
        return pattern.test(name) && p.status !== "已关闭";
    });
    
    if (filteredFiles.length === 0) {
        dv.paragraph("没有找到符合条件的技术债文件");
        return;
    }
    
    const specialFiles = filteredFiles.filter(p => !p.type || p.type === combinedType);
    
    if (specialFiles.length > 0) {
        dv.el("p", `当前共计 **${specialFiles.length}** 个文件的类型未被定义或无效`);
        dv.paragraph(specialFiles.map(p => `[[${p.file.path}|${p.aliases}]]`));
    } else {
        const groups = {};
        filteredFiles.forEach(p => {
            const type = p.type || "未分类";
            if (!groups[type]) groups[type] = [];
            groups[type].push(p);
        });
        
        const sortedGroups = Object.entries(groups)
            .map(([type, files]) => ({ type, count: files.length, files }))
            .sort((a, b) => b.count - a.count);
        
        const topGroups = sortedGroups.slice(0, 3);
        
        topGroups.forEach(group => {
            dv.el("p", `${group.type} (${group.count})`);
            dv.paragraph(group.files.map(p => `[[${p.file.path}|${p.aliases}]]`));
        });
    }
}

techDebtReport(input);